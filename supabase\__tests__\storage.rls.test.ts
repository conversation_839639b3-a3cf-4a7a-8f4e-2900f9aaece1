import { test, expect, beforeAll, afterAll, describe } from "vitest";
import { createSetupHooks } from "./utils/createSetupHooks";
import { readFileSync } from "fs";
import { fileTypeFromBuffer } from "file-type";
import { mockCustomer, mockProvider, mockAdmin } from "./mocks/auth.user";
import { mockCatalogActivity } from "./mocks/app_catalog.activity";
import { mockProviderActivity } from "./mocks/app_provider.activity";
import { serviceClient } from "./utils/client";

createSetupHooks();

const customer = mockCustomer();
const provider = mockProvider();
const admin = mockAdmin();

// Test files
const avatarPath = "supabase/__tests__/objects/test-avatar.jpg";
const galleryPath1 = "supabase/__tests__/objects/test-gallery-1.webp";
const galleryPath2 = "supabase/__tests__/objects/test-gallery-2.webp";
const galleryPath3 = "supabase/__tests__/objects/test-gallery-3.webp";
const voicePath = "supabase/__tests__/objects/test-voice.mp3";

// Mock activities for testing
const catalogActivity = mockCatalogActivity({ admin });
mockProviderActivity({ provider, catalogActivity });

describe("Account Avatar Storage RLS", () => {
  describe("Authenticated user with own files", () => {
    test("Can insert avatar", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!customer.client) throw new Error("Customer client is undefined");

      const file = readFileSync(avatarPath);
      const fileType = await fileTypeFromBuffer(file);

      const insertResponse = await customer.client.storage
        .from("account_avatar")
        .upload(customer.data.id, file, {
          upsert: true,
          metadata: {
            description: "insert test"
          },
          contentType: fileType?.mime
        });

      expect(insertResponse.error).toBeNull();

      const insertCheckResponse = await customer.client.storage
        .from("account_avatar")
        .info(customer.data.id);

      expect(insertCheckResponse.data?.metadata?.description).toBe(
        "insert test"
      );
    });

    test("Can update avatar", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!customer.client) throw new Error("Customer client is undefined");

      const file = readFileSync(avatarPath);
      const fileType = await fileTypeFromBuffer(file);

      const updateResponse = await customer.client.storage
        .from("account_avatar")
        .upload(customer.data.id, file, {
          upsert: true,
          metadata: {
            description: "update test"
          },
          contentType: fileType?.mime
        });

      expect(updateResponse.error).toBeNull();

      const updateCheckResponse = await customer.client.storage
        .from("account_avatar")
        .info(customer.data.id);

      expect(updateCheckResponse.data?.metadata?.description).toBe(
        "update test"
      );
    });

    test("Can delete avatar", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!customer.client) throw new Error("Customer client is undefined");

      await customer.client.storage
        .from("account_avatar")
        .remove([customer.data.id]);

      const deleteCheckResponse = await customer.client.storage
        .from("account_avatar")
        .info(customer.data.id);

      expect(deleteCheckResponse.data?.name).toBeUndefined();
    });
  });

  describe("Authenticated user with other user's files", () => {
    beforeAll(async () => {
      const file = readFileSync(avatarPath);
      const contentType = (await fileTypeFromBuffer(file))?.mime;

      const { error } = await serviceClient.storage
        .from("account_avatar")
        .upload("not-user-id", file, { upsert: true, contentType });

      if (error) throw error;
    });

    afterAll(async () => {
      const { error } = await serviceClient.storage
        .from("account_avatar")
        .remove(["not-user-id"]);

      if (error) throw error;
    });

    test("Cannot insert with other user's ID", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");

      const file = readFileSync(avatarPath);
      const contentType = (await fileTypeFromBuffer(file))?.mime;

      const insertResponse = await customer.client.storage
        .from("account_avatar")
        .upload("not-user-id-insert", file, { contentType });

      expect(insertResponse.error).not.toBeNull();

      const insertCheckResponse = await customer.client.storage
        .from("account_avatar")
        .info("not-user-id-insert");

      expect(insertCheckResponse.data?.name).toBeUndefined();
    });

    test("Cannot update other user's file", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");

      const file = readFileSync(avatarPath);
      const contentType = (await fileTypeFromBuffer(file))?.mime;

      const updateResponse = await customer.client.storage
        .from("account_avatar")
        .upload("not-user-id", file, { contentType });

      expect(updateResponse.error).not.toBeNull();
    });

    test("Cannot delete other user's file", async () => {
      if (!customer.client) throw new Error("Customer client is undefined");

      await customer.client.storage
        .from("account_avatar")
        .remove(["not-user-id"]);

      const deleteCheckResponse = await customer.client.storage
        .from("account_avatar")
        .info("not-user-id");

      expect(deleteCheckResponse.data?.name).toBe("not-user-id");
    });
  });
});

describe("Provider Gallery Storage RLS", () => {
  describe("Provider with correct capabilities", () => {
    test("Can insert gallery image with correct filename pattern", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const file = readFileSync(galleryPath1);
      const fileType = await fileTypeFromBuffer(file);
      const filename = `${provider.data.id}_gallery_1`;

      const insertResponse = await provider.client.storage
        .from("provider_gallery")
        .upload(filename, file, {
          upsert: true,
          metadata: {
            description: "gallery test 1"
          },
          contentType: fileType?.mime
        });

      expect(insertResponse.error).toBeNull();

      const insertCheckResponse = await provider.client.storage
        .from("provider_gallery")
        .info(filename);

      expect(insertCheckResponse.data?.metadata?.description).toBe(
        "gallery test 1"
      );
    });

    test("Can upload multiple gallery images", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const files = [
        { path: galleryPath2, filename: `${provider.data.id}_gallery_2` },
        { path: galleryPath3, filename: `${provider.data.id}_gallery_3` }
      ];

      for (const { path, filename } of files) {
        const file = readFileSync(path);
        const fileType = await fileTypeFromBuffer(file);

        const insertResponse = await provider.client.storage
          .from("provider_gallery")
          .upload(filename, file, {
            upsert: true,
            contentType: fileType?.mime
          });

        expect(insertResponse.error).toBeNull();
      }
    });

    test("Can update gallery image", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const file = readFileSync(galleryPath1);
      const fileType = await fileTypeFromBuffer(file);
      const filename = `${provider.data.id}_gallery_1`;

      const updateResponse = await provider.client.storage
        .from("provider_gallery")
        .upload(filename, file, {
          upsert: true,
          metadata: {
            description: "updated gallery test"
          },
          contentType: fileType?.mime
        });

      expect(updateResponse.error).toBeNull();

      const updateCheckResponse = await provider.client.storage
        .from("provider_gallery")
        .info(filename);

      expect(updateCheckResponse.data?.metadata?.description).toBe(
        "updated gallery test"
      );
    });

    test("Can delete gallery image", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const filename = `${provider.data.id}_gallery_1`;

      await provider.client.storage.from("provider_gallery").remove([filename]);

      const deleteCheckResponse = await provider.client.storage
        .from("provider_gallery")
        .info(filename);

      expect(deleteCheckResponse.data?.name).toBeUndefined();
    });
  });

  describe("Provider with incorrect filename patterns", () => {
    test("Cannot insert with wrong filename pattern", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const file = readFileSync(galleryPath1);
      const fileType = await fileTypeFromBuffer(file);
      const wrongFilename = "wrong_filename_pattern";

      const insertResponse = await provider.client.storage
        .from("provider_gallery")
        .upload(wrongFilename, file, {
          contentType: fileType?.mime
        });

      expect(insertResponse.error).not.toBeNull();
    });

    test("Cannot insert with other user's ID in filename", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const file = readFileSync(galleryPath1);
      const fileType = await fileTypeFromBuffer(file);
      const wrongFilename = "other-user-id_gallery_1";

      const insertResponse = await provider.client.storage
        .from("provider_gallery")
        .upload(wrongFilename, file, {
          contentType: fileType?.mime
        });

      expect(insertResponse.error).not.toBeNull();
    });
  });

  describe("Customer without provider capabilities", () => {
    test("Cannot insert gallery image", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!customer.client) throw new Error("Customer client is undefined");

      const file = readFileSync(galleryPath1);
      const fileType = await fileTypeFromBuffer(file);
      const filename = `${customer.data.id}_gallery_1`;

      const insertResponse = await customer.client.storage
        .from("provider_gallery")
        .upload(filename, file, {
          contentType: fileType?.mime
        });

      expect(insertResponse.error).not.toBeNull();
    });
  });
});

describe("Provider Activity Storage RLS", () => {
  describe("Provider with valid activity", () => {
    test("Can insert activity image with correct filename pattern", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");
      if (!catalogActivity.id)
        throw new Error("Catalog activity ID is undefined");

      const file = readFileSync(galleryPath1);
      const fileType = await fileTypeFromBuffer(file);
      const filename = `${provider.data.id}_${catalogActivity.id}`;

      const insertResponse = await provider.client.storage
        .from("provider_activity")
        .upload(filename, file, {
          upsert: true,
          metadata: {
            description: "activity image test"
          },
          contentType: fileType?.mime
        });

      expect(insertResponse.error).toBeNull();

      const insertCheckResponse = await provider.client.storage
        .from("provider_activity")
        .info(filename);

      expect(insertCheckResponse.data?.metadata?.description).toBe(
        "activity image test"
      );
    });

    test("Can update activity image", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");
      if (!catalogActivity.id)
        throw new Error("Catalog activity ID is undefined");

      const file = readFileSync(galleryPath2);
      const fileType = await fileTypeFromBuffer(file);
      const filename = `${provider.data.id}_${catalogActivity.id}`;

      const updateResponse = await provider.client.storage
        .from("provider_activity")
        .upload(filename, file, {
          upsert: true,
          metadata: {
            description: "updated activity image"
          },
          contentType: fileType?.mime
        });

      expect(updateResponse.error).toBeNull();

      const updateCheckResponse = await provider.client.storage
        .from("provider_activity")
        .info(filename);

      expect(updateCheckResponse.data?.metadata?.description).toBe(
        "updated activity image"
      );
    });

    test("Can delete activity image", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");
      if (!catalogActivity.id)
        throw new Error("Catalog activity ID is undefined");

      const filename = `${provider.data.id}_${catalogActivity.id}`;

      await provider.client.storage
        .from("provider_activity")
        .remove([filename]);

      const deleteCheckResponse = await provider.client.storage
        .from("provider_activity")
        .info(filename);

      expect(deleteCheckResponse.data?.name).toBeUndefined();
    });
  });

  describe("Provider with invalid activity references", () => {
    test("Cannot insert with non-existent activity ID", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const file = readFileSync(galleryPath1);
      const fileType = await fileTypeFromBuffer(file);
      const filename = `${provider.data.id}_non-existent-activity-id`;

      const insertResponse = await provider.client.storage
        .from("provider_activity")
        .upload(filename, file, {
          contentType: fileType?.mime
        });

      expect(insertResponse.error).not.toBeNull();
    });

    test("Cannot insert with other user's activity", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const file = readFileSync(galleryPath1);
      const fileType = await fileTypeFromBuffer(file);
      const filename = `other-user-id_some-activity-id`;

      const insertResponse = await provider.client.storage
        .from("provider_activity")
        .upload(filename, file, {
          contentType: fileType?.mime
        });

      expect(insertResponse.error).not.toBeNull();
    });
  });

  describe("Customer without provider capabilities", () => {
    test("Cannot insert activity image", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!customer.client) throw new Error("Customer client is undefined");

      const file = readFileSync(galleryPath1);
      const fileType = await fileTypeFromBuffer(file);
      const filename = `${customer.data.id}_some-activity-id`;

      const insertResponse = await customer.client.storage
        .from("provider_activity")
        .upload(filename, file, {
          contentType: fileType?.mime
        });

      expect(insertResponse.error).not.toBeNull();
    });
  });
});

describe("Provider Voice Storage RLS", () => {
  describe("Provider with valid capabilities", () => {
    test("Can insert voice file with user ID filename", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const file = readFileSync(voicePath);
      const fileType = await fileTypeFromBuffer(file);
      const filename = provider.data.id;

      const insertResponse = await provider.client.storage
        .from("provider_voice")
        .upload(filename, file, {
          upsert: true,
          metadata: {
            description: "provider voice test"
          },
          contentType: fileType?.mime
        });

      expect(insertResponse.error).toBeNull();

      const insertCheckResponse = await provider.client.storage
        .from("provider_voice")
        .info(filename);

      expect(insertCheckResponse.data?.metadata?.description).toBe(
        "provider voice test"
      );
    });

    test("Can insert voice file with activity-specific filename", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");
      if (!catalogActivity.id)
        throw new Error("Catalog activity ID is undefined");

      const file = readFileSync(voicePath);
      const fileType = await fileTypeFromBuffer(file);
      const filename = `${provider.data.id}_${catalogActivity.id}`;

      const insertResponse = await provider.client.storage
        .from("provider_voice")
        .upload(filename, file, {
          upsert: true,
          metadata: {
            description: "activity voice test"
          },
          contentType: fileType?.mime
        });

      expect(insertResponse.error).toBeNull();

      const insertCheckResponse = await provider.client.storage
        .from("provider_voice")
        .info(filename);

      expect(insertCheckResponse.data?.metadata?.description).toBe(
        "activity voice test"
      );
    });

    test("Can update voice file", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const file = readFileSync(voicePath);
      const fileType = await fileTypeFromBuffer(file);
      const filename = provider.data.id;

      const updateResponse = await provider.client.storage
        .from("provider_voice")
        .upload(filename, file, {
          upsert: true,
          metadata: {
            description: "updated voice test"
          },
          contentType: fileType?.mime
        });

      expect(updateResponse.error).toBeNull();

      const updateCheckResponse = await provider.client.storage
        .from("provider_voice")
        .info(filename);

      expect(updateCheckResponse.data?.metadata?.description).toBe(
        "updated voice test"
      );
    });

    test("Can delete voice file", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const filename = provider.data.id;

      await provider.client.storage.from("provider_voice").remove([filename]);

      const deleteCheckResponse = await provider.client.storage
        .from("provider_voice")
        .info(filename);

      expect(deleteCheckResponse.data?.name).toBeUndefined();
    });
  });

  describe("Provider with invalid filename patterns", () => {
    test("Cannot insert with wrong filename pattern", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const file = readFileSync(voicePath);
      const fileType = await fileTypeFromBuffer(file);
      const wrongFilename = "wrong_filename_pattern";

      const insertResponse = await provider.client.storage
        .from("provider_voice")
        .upload(wrongFilename, file, {
          contentType: fileType?.mime
        });

      expect(insertResponse.error).not.toBeNull();
    });

    test("Cannot insert with non-existent activity ID", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const file = readFileSync(voicePath);
      const fileType = await fileTypeFromBuffer(file);
      const filename = `${provider.data.id}_non-existent-activity-id`;

      const insertResponse = await provider.client.storage
        .from("provider_voice")
        .upload(filename, file, {
          contentType: fileType?.mime
        });

      expect(insertResponse.error).not.toBeNull();
    });

    test("Cannot insert with other user's ID", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const file = readFileSync(voicePath);
      const fileType = await fileTypeFromBuffer(file);
      const wrongFilename = "other-user-id";

      const insertResponse = await provider.client.storage
        .from("provider_voice")
        .upload(wrongFilename, file, {
          contentType: fileType?.mime
        });

      expect(insertResponse.error).not.toBeNull();
    });
  });

  describe("Customer without provider capabilities", () => {
    test("Cannot insert voice file", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!customer.client) throw new Error("Customer client is undefined");

      const file = readFileSync(voicePath);
      const fileType = await fileTypeFromBuffer(file);
      const filename = customer.data.id;

      const insertResponse = await customer.client.storage
        .from("provider_voice")
        .upload(filename, file, {
          contentType: fileType?.mime
        });

      expect(insertResponse.error).not.toBeNull();
    });
  });
});

describe("Storage Edge Cases", () => {
  describe("MIME type restrictions", () => {
    test("Cannot upload non-image file to image bucket", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");
      if (!customer.client) throw new Error("Customer client is undefined");

      const file = readFileSync(voicePath); // Audio file

      const insertResponse = await customer.client.storage
        .from("account_avatar")
        .upload(customer.data.id, file, {
          contentType: "audio/mpeg"
        });

      expect(insertResponse.error).not.toBeNull();
      expect(insertResponse.error?.message).toContain("mime");
    });

    test("Cannot upload non-audio file to voice bucket", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!provider.client) throw new Error("Provider client is undefined");

      const file = readFileSync(avatarPath); // Image file

      const insertResponse = await provider.client.storage
        .from("provider_voice")
        .upload(provider.data.id, file, {
          contentType: "image/jpeg"
        });

      expect(insertResponse.error).not.toBeNull();
      expect(insertResponse.error?.message).toContain("mime");
    });
  });

  describe("Anonymous user access", () => {
    test("Anonymous user can view all buckets (public read)", async () => {
      // Test that all buckets allow public read access
      const buckets = [
        "account_avatar",
        "provider_gallery",
        "provider_activity",
        "provider_voice"
      ];

      for (const bucket of buckets) {
        const listResponse = await serviceClient.storage.from(bucket).list();

        // Should not error for public read access
        expect(listResponse.error).toBeNull();
      }
    });

    test("Anonymous user cannot upload to any bucket", async () => {
      if (!customer.data) throw new Error("Customer data is undefined");

      const file = readFileSync(avatarPath);
      const fileType = await fileTypeFromBuffer(file);

      // Create truly anonymous client (without authentication)
      const { createClient } = await import("@supabase/supabase-js");
      const anonClient = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );

      const insertResponse = await anonClient.storage
        .from("account_avatar")
        .upload(customer.data.id, file, {
          contentType: fileType?.mime
        });

      expect(insertResponse.error).not.toBeNull();
    });
  });

  describe("File cleanup", () => {
    test("Clean up any remaining test files", async () => {
      if (!provider.data) throw new Error("Provider data is undefined");
      if (!customer.data) throw new Error("Customer data is undefined");

      // Clean up any remaining files from tests
      const cleanupFiles = [
        { bucket: "account_avatar", files: [customer.data.id] },
        {
          bucket: "provider_gallery",
          files: [
            `${provider.data.id}_gallery_2`,
            `${provider.data.id}_gallery_3`
          ]
        },
        {
          bucket: "provider_voice",
          files: [`${provider.data.id}_${catalogActivity.id || "test"}`]
        }
      ];

      for (const { bucket, files } of cleanupFiles) {
        await serviceClient.storage.from(bucket).remove(files);
      }

      // Verify cleanup
      for (const { bucket, files } of cleanupFiles) {
        for (const file of files) {
          const checkResponse = await serviceClient.storage
            .from(bucket)
            .info(file);

          expect(checkResponse.data?.name).toBeUndefined();
        }
      }
    });
  });
});
