-- section SCHEMA
DROP POLICY IF EXISTS "account_avatar_select_all" ON STORAGE.objects
;

DROP POLICY IF EXISTS "account_avatar_insert_own" ON STORAGE.objects
;

DROP POLICY IF EXISTS "account_avatar_update_own" ON STORAGE.objects
;

DROP POLICY IF EXISTS "account_avatar_delete_own" ON STORAGE.objects
;

DROP POLICY IF EXISTS "provider_gallery_select_all" ON storage.objects
;

DROP POLICY IF EXISTS "provider_gallery_insert_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_gallery_update_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_gallery_delete_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_activity_select_all" ON storage.objects
;

DROP POLICY IF EXISTS "provider_activity_insert_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_activity_update_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_activity_delete_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_voice_select_all" ON storage.objects
;

DROP POLICY IF EXISTS "provider_voice_insert_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_voice_update_own" ON storage.objects
;

DROP POLICY IF EXISTS "provider_voice_delete_own" ON storage.objects
;

-- !section
-- section RLS POLICIES
-- anchor account_avatar
CREATE POLICY "account_avatar_select_all" ON STORAGE.objects FOR
SELECT
  USING (bucket_id = 'account_avatar')
;

CREATE POLICY "account_avatar_insert_own" ON STORAGE.objects FOR INSERT TO authenticated
WITH
  CHECK (
    bucket_id = 'account_avatar'
    AND (STORAGE.filename (NAME)) = (
      (
        SELECT
          auth.uid ()
      )::TEXT
    )
  )
;

CREATE POLICY "account_avatar_update_own" ON STORAGE.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'account_avatar'
    AND (STORAGE.filename (NAME)) = (
      (
        SELECT
          auth.uid ()
      )::TEXT
    )
  )
;

CREATE POLICY "account_avatar_delete_own" ON STORAGE.objects FOR DELETE TO authenticated USING (
  bucket_id = 'account_avatar'
  AND (STORAGE.filename (NAME)) = (
    (
      SELECT
        auth.uid ()
    )::TEXT
  )
)
;

-- anchor provider_gallery
CREATE POLICY "provider_gallery_select_all" ON storage.objects FOR
SELECT
  USING (
    bucket_id = 'provider_gallery'
  )
;

CREATE POLICY "provider_gallery_insert_own" ON storage.objects FOR insert TO authenticated
WITH
  CHECK (
    bucket_id = 'provider_gallery'
    AND app_access.has_capability (
      'storage.provider_gallery.edit'
    )
    AND (STORAGE.filename (NAME)) LIKE (
      (
        SELECT
          auth.uid ()
      )::TEXT || '_%'
    )
  )
;

CREATE POLICY "provider_gallery_update_own" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'provider_gallery'
    AND app_access.has_capability (
      'storage.provider_gallery.edit'
    )
    AND (STORAGE.filename (NAME)) LIKE (
      (
        SELECT
          auth.uid ()
      )::TEXT || '_%'
    )
  )
;

CREATE POLICY "provider_gallery_delete_own" ON storage.objects FOR DELETE TO authenticated USING (
  bucket_id = 'provider_gallery'
  AND app_access.has_capability (
    'storage.provider_gallery.edit'
  )
  AND (STORAGE.filename (NAME)) LIKE (
    (
      SELECT
        auth.uid ()
    )::TEXT || '_%'
  )
)
;

-- anchor provider_activity
CREATE POLICY "provider_activity_select_all" ON storage.objects FOR
SELECT
  USING (
    bucket_id = 'provider_activity'
  )
;

CREATE POLICY "provider_activity_insert_own" ON storage.objects FOR insert TO authenticated
WITH
  CHECK (
    bucket_id = 'provider_activity'
    AND app_access.has_capability (
      'storage.provider_activity.edit'
    )
    AND EXISTS (
      SELECT
        1
      FROM
        app_provider.activity a
      WHERE
        a.user_id = auth.uid ()
        AND (STORAGE.filename (NAME)) = auth.uid ()::TEXT || '_' || a.activity_id::TEXT
    )
  )
;

CREATE POLICY "provider_activity_update_own" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'provider_activity'
    AND app_access.has_capability (
      'storage.provider_activity.edit'
    )
    AND EXISTS (
      SELECT
        1
      FROM
        app_provider.activity a
      WHERE
        a.user_id = auth.uid ()
        AND (STORAGE.filename (NAME)) = auth.uid ()::TEXT || '_' || a.activity_id::TEXT
    )
  )
;

CREATE POLICY "provider_activity_delete_own" ON storage.objects FOR DELETE TO authenticated USING (
  bucket_id = 'provider_activity'
  AND app_access.has_capability (
    'storage.provider_activity.edit'
  )
  AND EXISTS (
    SELECT
      1
    FROM
      app_provider.activity a
    WHERE
      a.user_id = auth.uid ()
      AND (STORAGE.filename (NAME)) = auth.uid ()::TEXT || '_' || a.activity_id::TEXT
  )
)
;

-- anchor provider_voice
CREATE POLICY "provider_voice_select_all" ON storage.objects FOR
SELECT
  USING (bucket_id = 'provider_voice')
;

CREATE POLICY "provider_voice_insert_own" ON storage.objects FOR INSERT TO authenticated
WITH
  CHECK (
    bucket_id = 'provider_voice'
    AND app_access.has_capability ('storage.provider_voice.edit')
    AND (
      (STORAGE.filename (NAME)) = auth.uid ()::TEXT
      OR EXISTS (
        SELECT
          1
        FROM
          app_provider.activity a
        WHERE
          a.user_id = auth.uid ()
          AND (STORAGE.filename (NAME)) = auth.uid ()::TEXT || '_' || a.activity_id::TEXT
      )
    )
  )
;

CREATE POLICY "provider_voice_update_own" ON storage.objects
FOR UPDATE
  TO authenticated USING (
    bucket_id = 'provider_voice'
    AND app_access.has_capability ('storage.provider_voice.edit')
    AND (
      (STORAGE.filename (NAME)) = auth.uid ()::TEXT
      OR EXISTS (
        SELECT
          1
        FROM
          app_provider.activity a
        WHERE
          a.user_id = auth.uid ()
          AND (STORAGE.filename (NAME)) = auth.uid ()::TEXT || '_' || a.activity_id::TEXT
      )
    )
  )
;

CREATE POLICY "provider_voice_delete_own" ON storage.objects FOR DELETE TO authenticated USING (
  bucket_id = 'provider_voice'
  AND app_access.has_capability ('storage.provider_voice.edit')
  AND (
    (STORAGE.filename (NAME)) = auth.uid ()::TEXT
    OR EXISTS (
      SELECT
        1
      FROM
        app_provider.activity a
      WHERE
        a.user_id = auth.uid ()
        AND (STORAGE.filename (NAME)) = auth.uid ()::TEXT || '_' || a.activity_id::TEXT
    )
  )
)
;

-- !section
-- section CAPABILITIES
-- anchor provider
SELECT
  app_access.define_role_capability (
    'provider',
    ARRAY[
      'storage.provider_gallery.edit',
      'storage.provider_activity.edit',
      'storage.provider_voice.edit'
    ]
  )
;

-- anchor provider_applicant
SELECT
  app_access.define_role_capability (
    'provider_applicant',
    ARRAY[
      'storage.provider_gallery.edit',
      'storage.provider_activity.edit',
      'storage.provider_voice.edit'
    ]
  )
;

-- !section